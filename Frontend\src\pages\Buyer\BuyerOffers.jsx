import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { getBuyerOffers, cancelOffer } from "../../redux/slices/offerSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import Table from "../../components/common/Table";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaEye, FaTimes, FaSync, FaCreditCard, FaCheck } from "react-icons/fa";
import { MdLocalOffer } from "react-icons/md";
import { toast } from "react-toastify";
import "../../styles/BuyerOffers.css";

const BuyerOffers = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { buyerOffers, isLoading, isError, error } = useSelector((state) => state.offer);
  const [cancellingOfferId, setCancellingOfferId] = useState(null);

  useEffect(() => {
    dispatch(getBuyerOffers());
  }, [dispatch]);

  const handleCancelOffer = async (offerId) => {
    if (window.confirm("Are you sure you want to cancel this offer?")) {
      setCancellingOfferId(offerId);
      try {
        await dispatch(cancelOffer(offerId)).unwrap();
        toast.success("Offer cancelled successfully");
        dispatch(getBuyerOffers()); // Refresh the list
      } catch (error) {
        toast.error(error.message || "Failed to cancel offer");
      } finally {
        setCancellingOfferId(null);
      }
    }
  };

  const handleRetry = () => {
    dispatch(getBuyerOffers());
  };

  const handlePayNow = (offer) => {
    // Check if offer has an order and redirect to the existing checkout page
    if (offer.orderId && offer.orderId._id) {
      navigate(`/checkout/${offer.orderId._id}`);
    } else {
      toast.error("Order not found. Please contact support.");
    }
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      Pending: "status-pending",
      Accepted: "status-accepted",
      Rejected: "status-rejected",
      Cancelled: "status-cancelled",
      Expired: "status-expired"
    };

    return (
      <span className={`status-badge ${statusClasses[status] || ""}`}>
        {status}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };

  const formatPrice = (price) => {
    return `$${parseFloat(price).toFixed(2)}`;
  };

  const columns = [
    {
      header: "Content",
      accessor: "content",
      render: (offer) => (
        <div className="content-info">
          <div className="content-thumbnail">
            {offer.content?.thumbnailUrl ? (
              <img src={offer.content.thumbnailUrl} alt={offer.content.title} />
            ) : (
              <div className="no-thumbnail">
                <MdLocalOffer />
              </div>
            )}
          </div>
          <div className="content-details">
            <h4 className="content-title">{offer.content?.title || "Untitled"}</h4>
            <p className="content-sport">{offer.content?.sport || "N/A"}</p>
          </div>
        </div>
      )
    },
    {
      header: "Seller",
      accessor: "seller",
      render: (offer) => (
        <div className="seller-info">
          <span className="seller-name">
            {offer.seller?.firstName} {offer.seller?.lastName}
          </span>
          <span className="seller-email">{offer.seller?.email}</span>
        </div>
      )
    },
    {
      header: "Offer Amount",
      accessor: "amount",
      render: (offer) => (
        <span className="offer-amount">{formatPrice(offer.amount)}</span>
      )
    },
    {
      header: "Status",
      accessor: "status",
      render: (offer) => getStatusBadge(offer.status)
    },
    {
      header: "Date",
      accessor: "createdAt",
      render: (offer) => (
        <span className="offer-date">{formatDate(offer.createdAt)}</span>
      )
    },
    {
      header: "Actions",
      accessor: "actions",
      render: (offer) => (
        <div className="action-buttons">
          <Link
            to={`/buyer/details/${offer.content?._id}`}
            className="btn-icon btn-view"
            title="View Content"
          >
            <FaEye />
          </Link>
          {offer.status === "Accepted" ? (
            // Check if payment is completed
            offer.orderId && offer.orderId.paymentStatus === "Completed" ? (
              <button
                className="btn-paid"
                disabled
                style={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  cursor: 'not-allowed',
                  opacity: 0.8,
                  padding: '8px 12px',
                  borderRadius: '4px',
                  border: 'none',
                  fontSize: '12px'
                }}
              >
                <FaCheck /> Already Paid
              </button>
            ) : (
              <button
                className="btn-pay"
                onClick={() => handlePayNow(offer)}
                style={{
                  backgroundColor: '#2c5aa0',
                  color: 'white',
                  padding: '8px 12px',
                  borderRadius: '4px',
                  border: 'none',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                <FaCreditCard /> Pay Now
              </button>
            )
          ) : offer.status === "Pending" ? (
            <button
              className="btn-icon btn-cancel"
              onClick={() => handleCancelOffer(offer._id)}
              disabled={cancellingOfferId === offer._id}
              title="Cancel Offer"
            >
              {cancellingOfferId === offer._id ? <FaSync className="spinning" /> : <FaTimes />}
            </button>
          ) : null}
        </div>
      )
    }
  ];

  if (isError) {
    return (
      <SectionWrapper
        icon={<MdLocalOffer className="BuyerSidebar__icon" />}
        title="My Offers"
      >
        <ErrorDisplay
          error={error}
          onRetry={handleRetry}
          title="Failed to load offers"
        />
      </SectionWrapper>
    );
  }

  return (
    <SectionWrapper
      icon={<MdLocalOffer className="BuyerSidebar__icon" />}
      title="My Offers"
    >
      <div className="BuyerOffers">
        {isLoading ? (
          <LoadingSkeleton type="table" rows={5} />
        ) : buyerOffers && buyerOffers.length > 0 ? (
          <>
            <div className="offers-summary">
              <p>You have made {buyerOffers.length} offer{buyerOffers.length !== 1 ? 's' : ''}</p>
            </div>
            <Table
              columns={columns}
              data={buyerOffers}
              className="offers-table"
            />
          </>
        ) : (
          <div className="no-offers">
            <MdLocalOffer className="no-offers-icon" />
            <h3>No Offers Yet</h3>
            <p>You haven't made any offers yet. Browse content and make your first offer!</p>
            <Link to="/buyer/browse" className="btn-primary">
              Browse Content
            </Link>
          </div>
        )}
      </div>
    </SectionWrapper>
  );
};

export default BuyerOffers;
